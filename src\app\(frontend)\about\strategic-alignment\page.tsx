import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import { NPIScrollToTop } from '@/components/ui/npi-scroll-to-top'
import PageClient from './page.client'

export const metadata: Metadata = {
  title: 'Strategic Alignment - About NPI',
  description:
    "Learn about NPI's strategic alignment with Kenya's key development frameworks including Vision 2030, MTP IV, BeTA, and National Museums of Kenya Strategic Plan.",
}

const strategicAlignmentPageLayout = [
  {
    blockType: 'npiStrategicAlignmentHero' as const,
  },
  {
    blockType: 'npiStrategicAlignment' as const,
  },
]

export default function StrategicAlignmentPage() {
  return (
    <>
      <PageClient />
      <article className="pb-12" style={{ scrollBehavior: 'smooth' }}>
        <RenderBlocks blocks={strategicAlignmentPageLayout} />
      </article>
      <NPIScrollToTop showAfter={400} />
    </>
  )
}
