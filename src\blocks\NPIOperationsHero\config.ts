import type { Block } from 'payload'

export const NPIOperationsHero: Block = {
  slug: 'npiOperationsHero',
  interfaceName: 'NPIOperationsHeroBlock',
  fields: [
    {
      name: 'title',
      type: 'text',
      defaultValue: 'Operations & Structure',
    },
    {
      name: 'subtitle',
      type: 'text',
      defaultValue: 'Multi-agency framework driving sustainable development',
    },
    {
      name: 'backgroundImage',
      type: 'text',
      defaultValue: '/assets/partners structure.jpg',
    },
  ],
  labels: {
    plural: 'NPI Operations Heroes',
    singular: 'NPI Operations Hero',
  },
}
