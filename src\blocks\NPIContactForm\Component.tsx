'use client'

import React, { useState } from 'react'
import { NPISection, NPISectionHeader, NPISectionTitle, NPISectionDescription } from '@/components/ui/npi-section'
import { NPI<PERSON>ard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import { Mail, Phone, MapPin, Clock, Send, User, MessageSquare } from 'lucide-react'

interface ContactInfo {
  icon: React.ReactNode
  title: string
  details: string[]
}

interface NPIContactFormProps {
  title?: string
  description?: string
  contactInfo?: ContactInfo[]
}

export const NPIContactFormBlock: React.FC<NPIContactFormProps> = ({
  title = "Contact Us",
  description = "Get in touch with the NPI team. We're here to answer your questions, provide information, and explore collaboration opportunities.",
  contactInfo = [
    {
      icon: <MapPin className="w-6 h-6" />,
      title: "Office Address",
      details: [
        "National Museums of Kenya",
        "Museum Hill Road",
        "P.O. Box 40658-00100",
        "Nairobi, Kenya"
      ]
    },
    {
      icon: <Phone className="w-6 h-6" />,
      title: "Phone & Email",
      details: [
        "+254 20 374 2131",
        "+254 20 374 2161",
        "<EMAIL>",
        "<EMAIL>"
      ]
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: "Office Hours",
      details: [
        "Monday - Friday",
        "8:00 AM - 5:00 PM",
        "Saturday: 9:00 AM - 1:00 PM",
        "Sunday: Closed"
      ]
    }
  ]
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    organization: '',
    subject: '',
    category: '',
    message: ''
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Reset form
    setFormData({
      name: '',
      email: '',
      organization: '',
      subject: '',
      category: '',
      message: ''
    })
    setIsSubmitting(false)
    alert('Thank you for your message! We will get back to you soon.')
  }

  const categories = [
    'General Inquiry',
    'Partnership Opportunity',
    'Research Collaboration',
    'Media & Press',
    'IKIA Database Access',
    'Program Information',
    'Technical Support',
    'Other'
  ]

  return (
    <NPISection>
      <NPISectionHeader>
        <NPISectionTitle>{title}</NPISectionTitle>
        <NPISectionDescription>{description}</NPISectionDescription>
      </NPISectionHeader>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Contact Information */}
        <div className="lg:col-span-1 space-y-6">
          {contactInfo.map((info, index) => (
            <NPICard key={index} className="hover:shadow-lg transition-shadow duration-300">
              <NPICardHeader>
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary">
                    {info.icon}
                  </div>
                  <NPICardTitle className="text-lg">{info.title}</NPICardTitle>
                </div>
              </NPICardHeader>
              <NPICardContent>
                <div className="space-y-1">
                  {info.details.map((detail, detailIndex) => (
                    <p key={detailIndex} className="text-muted-foreground font-npi">{detail}</p>
                  ))}
                </div>
              </NPICardContent>
            </NPICard>
          ))}

          {/* Social Media */}
          <NPICard>
            <NPICardHeader>
              <NPICardTitle className="text-lg">Follow Us</NPICardTitle>
            </NPICardHeader>
            <NPICardContent>
              <div className="flex gap-4">
                <a href="#" className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-white hover:bg-blue-700 transition-colors">
                  <span className="text-sm font-bold">f</span>
                </a>
                <a href="#" className="w-10 h-10 bg-blue-400 rounded-lg flex items-center justify-center text-white hover:bg-blue-500 transition-colors">
                  <span className="text-sm font-bold">t</span>
                </a>
                <a href="#" className="w-10 h-10 bg-blue-700 rounded-lg flex items-center justify-center text-white hover:bg-blue-800 transition-colors">
                  <span className="text-sm font-bold">in</span>
                </a>
                <a href="#" className="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center text-white hover:bg-red-700 transition-colors">
                  <span className="text-sm font-bold">yt</span>
                </a>
              </div>
            </NPICardContent>
          </NPICard>
        </div>

        {/* Contact Form */}
        <div className="lg:col-span-2">
          <NPICard>
            <NPICardHeader>
              <NPICardTitle className="text-2xl flex items-center gap-3">
                <MessageSquare className="w-6 h-6 text-primary" />
                Send us a Message
              </NPICardTitle>
            </NPICardHeader>
            <NPICardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium mb-2 font-npi">
                      Full Name *
                    </label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full pl-10 pr-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi"
                        placeholder="Enter your full name"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium mb-2 font-npi">
                      Email Address *
                    </label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full pl-10 pr-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi"
                        placeholder="Enter your email address"
                      />
                    </div>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="organization" className="block text-sm font-medium mb-2 font-npi">
                      Organization
                    </label>
                    <input
                      type="text"
                      id="organization"
                      name="organization"
                      value={formData.organization}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi"
                      placeholder="Your organization (optional)"
                    />
                  </div>

                  <div>
                    <label htmlFor="category" className="block text-sm font-medium mb-2 font-npi">
                      Category *
                    </label>
                    <select
                      id="category"
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi"
                    >
                      <option value="">Select a category</option>
                      {categories.map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium mb-2 font-npi">
                    Subject *
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi"
                    placeholder="Brief subject of your message"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium mb-2 font-npi">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={6}
                    className="w-full px-4 py-3 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent font-npi resize-vertical"
                    placeholder="Please provide details about your inquiry..."
                  />
                </div>

                <div className="flex items-center gap-4">
                  <NPIButton 
                    type="submit" 
                    variant="primary" 
                    size="lg"
                    disabled={isSubmitting}
                    className="flex-1 sm:flex-none"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                        Sending...
                      </>
                    ) : (
                      <>
                        <Send className="w-5 h-5 mr-2" />
                        Send Message
                      </>
                    )}
                  </NPIButton>
                  
                  <p className="text-sm text-muted-foreground font-npi">
                    We typically respond within 24-48 hours
                  </p>
                </div>
              </form>
            </NPICardContent>
          </NPICard>
        </div>
      </div>

      {/* Map Section */}
      <div className="mt-12">
        <NPICard>
          <NPICardHeader>
            <NPICardTitle className="text-xl">Find Us</NPICardTitle>
          </NPICardHeader>
          <NPICardContent>
            <div className="h-64 bg-muted rounded-lg flex items-center justify-center">
              <div className="text-center">
                <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground font-npi">Interactive map will be integrated here</p>
                <p className="text-sm text-muted-foreground font-npi">National Museums of Kenya, Museum Hill Road, Nairobi</p>
              </div>
            </div>
          </NPICardContent>
        </NPICard>
      </div>
    </NPISection>
  )
}
