import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import { NPIScrollToTop } from '@/components/ui/npi-scroll-to-top'
import PageClient from './page.client'

export const metadata: Metadata = {
  title: 'About NPI - Natural Products Industry Initiative',
  description:
    "Learn about the Natural Products Industry Initiative, our mission, vision, strategic alignment, and multi-agency approach to transforming Kenya's natural heritage into sustainable economic opportunities.",
}

const aboutPageLayout = [
  {
    blockType: 'npiAboutHero' as const,
  },
  {
    blockType: 'npiIntroduction' as const,
  },
  {
    blockType: 'npiHistoryTimeline' as const,
  },
  {
    blockType: 'npiMissionVision' as const,
  },
]

export default function AboutPage() {
  return (
    <>
      <PageClient />
      <article className="pb-12" style={{ scrollBehavior: 'smooth' }}>
        <RenderBlocks blocks={aboutPageLayout} />
      </article>
      <NPIScrollToTop showAfter={400} />
    </>
  )
}
