# Natural Products Industry (NPI) Initiative - Design System

## Overview

This document outlines the comprehensive design system created for the Natural Products Industry Initiative website, following the high-fidelity design requirements and modern UI/UX best practices.

## Design Philosophy

The NPI design system reflects Kenya's rich natural heritage while maintaining a professional, authoritative, and forward-looking aesthetic. It combines traditional elements with modern design principles to create an engaging and trustworthy digital presence.

## Color Palette

### Primary Colors
- **NPI Brown**: `hsl(25, 30%, 15%)` - Primary dark brown for headers and primary actions
- **NPI Green**: `hsl(120, 40%, 20%)` - Primary dark green for secondary actions and accents
- **NPI Cream**: `hsl(45, 25%, 95%)` - Cream/off-white for backgrounds and light elements
- **NPI Grey**: `hsl(0, 0%, 25%)` - Dark grey for body text and content

### Accent Colors
- **NPI Accent**: `hsl(35, 60%, 45%)` - Earthy accent color for highlights and CTAs

### Semantic Colors
- **Success**: `hsl(120, 50%, 35%)` - Green for success states
- **Warning**: `hsl(35, 80%, 55%)` - Orange for warning states
- **Error**: `hsl(0, 70%, 50%)` - Red for error states

## Typography

### Primary Font
- **Myriad Pro** - Used throughout the website for all text elements
- Fallback: `system-ui, sans-serif`
- Font weights: 300 (Light), 400 (Regular), 600 (Semibold), 700 (Bold)

### Typography Scale
- **Heading 1**: `text-4xl lg:text-5xl xl:text-6xl font-bold` (Hero titles)
- **Heading 2**: `text-3xl lg:text-4xl font-bold` (Section titles)
- **Heading 3**: `text-2xl lg:text-3xl font-semibold` (Subsection titles)
- **Large Text**: `text-lg lg:text-xl` (Descriptions and lead text)
- **Body Text**: `text-base` (Regular content)

## Components

### Core UI Components

#### NPIButton
- Variants: `default`, `brown`, `green`, `cream`, `accent`, `outline`, `ghost`, `link`
- Sizes: `sm`, `default`, `lg`, `xl`, `icon`
- Features: Hover states, focus rings, disabled states

#### NPICard
- Clean card design with subtle shadows
- Hover effects for interactive elements
- Consistent padding and spacing
- Includes: Header, Title, Description, Content, Footer

#### NPISection
- Variants: `default`, `primary`, `secondary`, `accent`, `pattern`
- Sizes: `sm`, `md`, `lg`, `xl`
- Container management and responsive spacing

#### NPIHero
- Variants: `default`, `gradient`, `image`
- Support for background images with overlay
- Responsive typography and spacing
- Action button integration

#### NPIStatistics
- Configurable grid layouts (2, 3, 4 columns)
- Icon support for visual enhancement
- Hover effects and animations
- Responsive design

#### NPIPartners
- Grid and scroll variants
- Logo optimization and hover effects
- Responsive layout management
- Optional partner names display

### Block Components

#### NPIIntroduction
- Two-column layout with content and statistics
- Highlight cards with icons
- Call-to-action buttons
- Partner information display

#### NPIMissionVision
- Mission and Vision cards
- Core Values grid
- Strategic Themes overview
- Responsive card layouts

#### NPIFeaturedPrograms
- Program cards with images
- Status indicators (active, completed, upcoming)
- Category and location information
- Hover effects and transitions

#### NPISuccessStories
- Story cards with hero images
- Testimonial integration
- Impact metrics display
- Category-based organization

#### NPIStatistics
- Key performance indicators
- Icon-based visual elements
- Responsive grid layout
- Pattern background support

#### NPILatestUpdates
- Featured news layout
- Date and category information
- Image integration
- Read more functionality

#### NPIPartners
- Partner logo grid
- Hover effects and links
- Responsive organization
- Partner categorization

## Layout System

### Container
- **npi-container**: `max-w-7xl mx-auto px-4 sm:px-6 lg:px-8`
- Consistent horizontal spacing across all screen sizes

### Sections
- **npi-section**: Standardized vertical spacing (`py-16 lg:py-24`)
- Responsive padding adjustments

### Grid System
- CSS Grid and Flexbox for responsive layouts
- Breakpoints: `sm` (640px), `md` (768px), `lg` (1024px), `xl` (1280px), `2xl` (1536px)

## Responsive Design

### Breakpoint Strategy
- **Mobile First**: Base styles for mobile devices
- **Progressive Enhancement**: Layer on tablet and desktop styles
- **Flexible Grids**: Adapt to different screen sizes
- **Scalable Typography**: Responsive font sizes

### Key Responsive Features
- Navigation collapses on mobile
- Grid layouts adapt from 1 column to 4 columns
- Typography scales appropriately
- Images and media are fully responsive
- Touch-friendly interactive elements

## Accessibility

### WCAG 2.1 AA Compliance
- **Color Contrast**: All text meets minimum contrast ratios
- **Focus States**: Clear focus indicators for keyboard navigation
- **Alt Text**: Comprehensive image descriptions
- **Semantic HTML**: Proper heading hierarchy and landmarks
- **Screen Reader Support**: ARIA labels and descriptions

### Interactive Elements
- Keyboard navigation support
- Focus management
- Clear error states and messaging
- Consistent interaction patterns

## Animation and Transitions

### Subtle Animations
- **Hover Effects**: Color transitions, shadow changes
- **Focus States**: Ring animations and color shifts
- **Loading States**: Smooth transitions between states
- **Scroll Animations**: Subtle reveal effects (future enhancement)

### Performance Considerations
- CSS-based animations for optimal performance
- Reduced motion support for accessibility
- Minimal animation duration (200-300ms)

## File Structure

```
src/
├── components/ui/
│   ├── npi-button.tsx
│   ├── npi-card.tsx
│   ├── npi-section.tsx
│   ├── npi-hero.tsx
│   ├── npi-statistics.tsx
│   └── npi-partners.tsx
├── blocks/
│   ├── NPIIntroduction/
│   ├── NPIMissionVision/
│   ├── NPIFeaturedPrograms/
│   ├── NPISuccessStories/
│   ├── NPIStatistics/
│   ├── NPILatestUpdates/
│   └── NPIPartners/
├── heros/
│   └── NPIHero/
└── app/(frontend)/
    └── globals.css
```

## Usage Guidelines

### Component Usage
1. Always use NPI-specific components for consistency
2. Follow the established color palette
3. Maintain proper spacing using the section system
4. Use semantic HTML elements
5. Include proper accessibility attributes

### Content Guidelines
1. Use clear, concise language
2. Maintain consistent terminology
3. Include proper image alt text
4. Structure content with proper headings
5. Provide clear calls-to-action

## Future Enhancements

### Planned Features
1. Dark mode optimization
2. Advanced animation system
3. Component documentation site
4. Design token system
5. Automated accessibility testing

### Performance Optimizations
1. Image optimization pipeline
2. Component lazy loading
3. CSS optimization
4. Bundle size monitoring

## Browser Support

- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Mobile Browsers**: iOS Safari 14+, Chrome Mobile 90+
- **Progressive Enhancement**: Graceful degradation for older browsers

## Maintenance

### Regular Updates
- Monitor design system usage
- Update components based on feedback
- Maintain accessibility standards
- Performance monitoring and optimization
- Documentation updates

This design system provides a solid foundation for the NPI website while maintaining flexibility for future enhancements and expansions.
