'use client'

import React from 'react'
import { motion } from 'framer-motion'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardTitle } from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, ArrowRight } from 'lucide-react'

interface NewsItem {
  title: string
  excerpt: string
  image: string
  date: string
  category: string
  link: string
  featured?: boolean
}

interface NPILatestUpdatesProps {
  title?: string
  description?: string
  news?: NewsItem[]
}

export const NPILatestUpdatesBlock: React.FC<NPILatestUpdatesProps> = ({
  title = 'Latest Updates',
  description = "Stay informed about our latest developments, achievements, and upcoming initiatives in Kenya's natural products sector.",
  news = [
    {
      title: 'NPI Launches Comprehensive Knowledge Documentation Platform',
      excerpt:
        'Revolutionary digital platform now provides access to over 1,200 documented indigenous knowledge assets, supporting researchers and entrepreneurs nationwide.',
      image: '/assets/background.jpg',
      date: '2024-01-15',
      category: 'Platform Launch',
      link: '/news/knowledge-platform-launch',
      featured: true,
    },
    {
      title: 'Successful IP Registration for Maasai Traditional Medicine',
      excerpt:
        'Historic achievement as Maasai community secures intellectual property rights for traditional healing formulations, setting precedent for indigenous knowledge protection.',
      image: '/assets/product 1.jpg',
      date: '2024-01-10',
      category: 'IP Protection',
      link: '/news/maasai-ip-success',
    },
    {
      title: "Women's Aloe Cooperative Wins National Innovation Award",
      excerpt:
        "Baringo County women's cooperative recognized for outstanding innovation in natural product development and community empowerment.",
      image: '/assets/product 2.jpg',
      date: '2024-01-05',
      category: 'Awards',
      link: '/news/aloe-cooperative-award',
    },
    {
      title: 'International Investment Forum Attracts $50M Commitments',
      excerpt:
        "NPI's first International Natural Products Investment Forum successfully attracts significant funding commitments for community-based projects.",
      image: '/assets/product 3.jpg',
      date: '2023-12-20',
      category: 'Investment',
      link: '/news/investment-forum-success',
    },
  ],
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const featuredNews = news.find((item) => item.featured)
  const regularNews = news.filter((item) => !item.featured)

  return (
    <NPISection size="sm" className="bg-tertiary relative overflow-hidden pb-0">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-1/6 left-1/3 w-64 h-64 bg-primary/6 blur-3xl"
          animate={{
            x: [0, 35, 0],
            y: [0, -25, 0],
            scale: [1, 1.3, 1],
          }}
          transition={{
            duration: 16,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
        <motion.div
          className="absolute bottom-1/6 right-1/3 w-80 h-80 bg-npi-gold/5 blur-3xl"
          animate={{
            x: [0, -45, 0],
            y: [0, 30, 0],
            rotate: [0, 120, 240],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 3,
          }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <NPISectionHeader className="text-center mb-6">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="inline-flex items-center px-6 py-3 bg-primary/15 backdrop-blur-md border border-primary/30 text-primary text-sm font-semibold mb-4"
          >
            <Calendar className="w-4 h-4 mr-3" />
            Latest Updates
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <NPISectionTitle className="leading-[1.1] tracking-[-0.02em] mb-3 bg-gradient-to-r from-[#34170D] via-[#6E3C19] via-[#8A6240] to-[#A7795E] bg-clip-text text-transparent font-bold text-4xl">
              {title}
            </NPISectionTitle>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <NPISectionDescription className="font-light leading-[1.7] text-lg max-w-3xl mx-auto">
              {description}
            </NPISectionDescription>
          </motion.div>
        </NPISectionHeader>

        {/* Featured News - First Row */}
        <div className="grid lg:grid-cols-1 gap-6 mb-6 max-w-5xl mx-auto">
          {/* Featured News */}
          {featuredNews && (
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              whileHover={{ y: -8 }}
            >
              <NPICard className="overflow-hidden bg-gradient-to-br from-[#A7795E] to-[#8A6240] shadow-xl border-3 border-[#6E3C19] hover:border-[#34170D] hover:shadow-2xl hover:shadow-[#6E3C19]/40 group transition-all duration-300 aspect-[4/3] flex flex-col w-full">
                <div className="flex h-full">
                  {/* Image Section - Left Side */}
                  <div className="relative w-1/2 flex-shrink-0">
                    <Image
                      src={featuredNews.image}
                      alt={featuredNews.title}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-700"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-[#34170D]/70 via-[#6E3C19]/40 to-transparent" />
                    <div className="absolute top-4 left-4">
                      <motion.span
                        initial={{ scale: 0, rotate: -180 }}
                        whileInView={{ scale: 1, rotate: 0 }}
                        transition={{ delay: 0.3, type: 'spring' }}
                        className="bg-gradient-to-r from-[#102820] to-[#4C6444] text-[#E5E1DC] px-4 py-2 text-sm font-bold uppercase tracking-wide shadow-lg"
                      >
                        Featured
                      </motion.span>
                    </div>
                    <div className="absolute top-4 right-4">
                      <motion.div
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ delay: 0.5 }}
                        className="bg-gradient-to-r from-[#8A6240] to-[#A7795E] backdrop-blur-sm px-3 py-1.5 text-[#E5E1DC] text-sm font-bold shadow-lg"
                      >
                        {featuredNews.category}
                      </motion.div>
                    </div>
                  </div>

                  {/* Content Section - Right Side */}
                  <div className="w-1/2 p-6 flex flex-col justify-between bg-gradient-to-br from-[#E5E1DC]/95 to-[#CEC9BC]/90">
                    <div>
                      <div className="flex items-center gap-2 text-sm text-[#6E3C19] mb-4">
                        <Calendar className="w-4 h-4 text-[#8A6240]" />
                        <span className="font-bold">{formatDate(featuredNews.date)}</span>
                      </div>
                      <NPICardTitle className="text-xl font-bold text-[#34170D] leading-tight group-hover:text-[#6E3C19] transition-colors mb-3">
                        {featuredNews.title}
                      </NPICardTitle>
                      <p className="text-[#46372A] text-sm leading-relaxed line-clamp-3">
                        {featuredNews.excerpt}
                      </p>
                    </div>

                    <NPIButton
                      asChild
                      className="w-full bg-gradient-to-r from-[#6E3C19] to-[#8A6240] hover:from-[#8A6240] hover:to-[#A7795E] text-[#E5E1DC] font-medium transition-all duration-300 text-sm py-3 shadow-lg hover:shadow-xl"
                    >
                      <Link
                        href={featuredNews.link}
                        className="flex items-center justify-center gap-2"
                      >
                        Read More <ArrowRight className="w-4 h-4" />
                      </Link>
                    </NPIButton>
                  </div>
                </div>
              </NPICard>
            </motion.div>
          )}
        </div>

        {/* Regular News - Second Row */}
        <div className="grid lg:grid-cols-3 gap-6 mb-6 max-w-5xl mx-auto">
          {/* Regular News - Square Cards */}
          {regularNews.slice(0, 3).map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -8 }}
            >
              <NPICard
                className={`overflow-hidden shadow-xl border-3 hover:shadow-2xl group transition-all duration-300 aspect-square flex flex-col hover:scale-105 hover:-translate-y-2 ${
                  index % 3 === 0
                    ? 'bg-gradient-to-br from-[#4C6444] to-[#102820] border-[#8D8F78] hover:border-[#4C6444] hover:shadow-[#4C6444]/40'
                    : index % 3 === 1
                      ? 'bg-gradient-to-br from-[#8A6240] to-[#6E3C19] border-[#A7795E] hover:border-[#8A6240] hover:shadow-[#8A6240]/40'
                      : 'bg-gradient-to-br from-[#A7795E] to-[#CABA9C] border-[#8D8F78] hover:border-[#A7795E] hover:shadow-[#A7795E]/40'
                }`}
              >
                <div className="relative h-1/2 w-full flex-shrink-0">
                  <Image
                    src={item.image}
                    alt={item.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div
                    className={`absolute inset-0 ${
                      index % 3 === 0
                        ? 'bg-gradient-to-t from-[#102820]/50 to-transparent'
                        : index % 3 === 1
                          ? 'bg-gradient-to-t from-[#46372A]/50 to-transparent'
                          : 'bg-gradient-to-t from-[#8A6240]/50 to-transparent'
                    }`}
                  />
                  <div className="absolute top-2 right-2">
                    <span
                      className={`px-2 py-1 text-xs font-bold text-[#E5E1DC] ${
                        index % 3 === 0
                          ? 'bg-[#4C6444]/80'
                          : index % 3 === 1
                            ? 'bg-[#8A6240]/80'
                            : 'bg-[#A7795E]/80'
                      }`}
                    >
                      {item.category}
                    </span>
                  </div>
                </div>

                <div className="h-1/2 p-3 flex flex-col justify-between">
                  <div>
                    <div className="flex items-center gap-2 text-xs text-[#46372A] mb-2">
                      <Calendar className="w-3 h-3 text-[#6E3C19]" />
                      <span className="font-medium">{formatDate(item.date)}</span>
                    </div>

                    <h3 className="font-bold text-sm leading-tight mb-2 text-[#2F2C29] line-clamp-2">
                      <Link
                        href={item.link}
                        className="hover:text-[#6E3C19] transition-colors group-hover:text-[#6E3C19]"
                      >
                        {item.title}
                      </Link>
                    </h3>
                  </div>

                  <NPIButton
                    asChild
                    className={`w-full font-medium transition-all duration-300 text-xs py-2 ${
                      index % 3 === 0
                        ? 'bg-gradient-to-r from-[#4C6444] to-[#102820] hover:from-[#102820] hover:to-[#4C6444]'
                        : index % 3 === 1
                          ? 'bg-gradient-to-r from-[#8A6240] to-[#46372A] hover:from-[#46372A] hover:to-[#8A6240]'
                          : 'bg-gradient-to-r from-[#A7795E] to-[#CABA9C] hover:from-[#CABA9C] hover:to-[#A7795E]'
                    } text-[#E5E1DC]`}
                  >
                    <Link href={item.link} className="flex items-center justify-center gap-1">
                      Read More <ArrowRight className="w-3 h-3" />
                    </Link>
                  </NPIButton>
                </div>
              </NPICard>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-center mt-4"
        >
          <NPIButton
            asChild
            size="lg"
            variant="outline"
            className="border-4 border-[#4C6444] text-[#4C6444] hover:bg-gradient-to-r hover:from-[#4C6444] hover:to-[#102820] hover:text-[#E5E1DC] font-bold px-12 py-5 transition-all duration-300 npi-hover-lift hover:shadow-xl hover:shadow-[#4C6444]/50 bg-gradient-to-r from-[#E5E1DC] to-[#CEC9BC]"
          >
            <Link href="/news">View All News & Updates</Link>
          </NPIButton>
        </motion.div>
      </div>
    </NPISection>
  )
}
