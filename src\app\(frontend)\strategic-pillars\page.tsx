import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import PageClient from './page.client'

export const metadata: Metadata = {
  title: 'Strategic Pillars - Natural Products Industry Initiative',
  description:
    "Explore NPI's four strategic pillars: Indigenous Knowledge Documentation, Product Development & Commercialization, Capacity Building & Empowerment, and Intellectual Property Protection.",
}

const strategicPillarsLayout = [
  {
    blockType: 'npiPillarsHero' as const,
  },
  {
    blockType: 'npiStrategicPillars' as const,
    id: 'pillars-detail',
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Pillar Implementation Progress',
    variant: 'secondary',
    size: 'tight',
  },
  {
    blockType: 'npiFeaturedPrograms' as const,
    title: 'Programs by Strategic Pillar',
    description: 'Discover how our programs align with and support each strategic pillar.',
  },
]

export default function StrategicPillarsPage() {
  return (
    <article>
      <PageClient />
      <RenderBlocks blocks={strategicPillarsLayout} />
    </article>
  )
}
