import React from 'react'
import Image from 'next/image'

interface NPINewsHeroProps {
  title?: string
  subtitle?: string
  backgroundImage?: string
}

export const NPINewsHeroBlock: React.FC<NPINewsHeroProps> = ({
  title = 'News & Media',
  backgroundImage = '/assets/product 2.jpg',
}) => {
  return (
    <section className="min-h-screen relative overflow-hidden -mt-16 pt-16">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full">
        <Image
          src={backgroundImage}
          alt="Hero background"
          fill
          priority
          className="w-full h-full object-cover"
          sizes="100vw"
        />
      </div>

      {/* Overlay with bright color variants */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#102820]/30 via-[#4C6444]/50 to-[#2F2C29]/70" />

      {/* Top Center Title */}
      <div className="absolute top-32 left-1/2 transform -translate-x-1/2 z-30 text-center max-w-5xl px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight text-white">
          {title}
        </h1>
      </div>

      {/* Bottom Left Text with bright colors */}
      <div className="absolute bottom-8 left-8 z-30 max-w-sm">
        <div className="bg-[#E5E1DC]/90 backdrop-blur-md p-6 border-l-4 border-[#8A6240]">
          <p className="text-sm sm:text-base md:text-lg leading-relaxed text-[#2F2C29]">
            Stay informed about the latest developments and insights from Kenya&apos;s natural
            products sector.
          </p>
        </div>
      </div>

      {/* Bottom Right Feature Card with bright colors */}
      <div className="absolute bottom-8 right-8 z-30">
        <div className="bg-[#CEC9BC]/95 backdrop-blur-md p-6 shadow-lg max-w-xs border border-[#8D8F78]/30">
          <h3 className="text-lg sm:text-xl font-bold mb-2 text-[#34170D]">Latest Updates</h3>
          <p className="text-sm sm:text-base mb-4 text-[#46372A]">
            Discover stories of innovation, community impact, and sustainable development across
            Kenya.
          </p>
          <a
            href="#latest-news"
            className="text-[#6E3C19] hover:text-[#A7795E] text-sm font-medium transition-colors"
          >
            Read Latest News &rarr;
          </a>
        </div>
      </div>
    </section>
  )
}
