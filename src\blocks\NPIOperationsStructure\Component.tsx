'use client'

import React, { useState } from 'react'
import {
  NPISection,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import { NPICard, NPICardHeader, NPICardTitle, NPICardContent } from '@/components/ui/npi-card'
import { NPIPartners } from '@/components/ui/npi-partners'
import { NPITeamModal, type TeamMember } from '@/components/ui/npi-modal'
import { Building, Users, Cog, Network, ExternalLink, User } from 'lucide-react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { useParallax } from '@/hooks/useParallax'
import { ScrollReveal, StaggerContainer, StaggerItem } from '@/components/ui/npi-scroll-reveal'

interface OperationalUnit {
  title: string
  description: string
  responsibilities: string[]
  icon: React.ReactNode
}

interface ImplementingPartner {
  name: string
  role: string
  description: string
  logo: string
  url?: string
  keyContributions: string[]
}

interface NPIOperationsStructureProps {
  title?: string
  description?: string
  operationalUnits?: OperationalUnit[]
  implementingPartners?: ImplementingPartner[]
  teamMembers?: TeamMember[]
}

export const NPIOperationsStructureBlock: React.FC<NPIOperationsStructureProps> = ({
  title = 'Operations & Structure',
  description = 'NPI operates through a collaborative multi-agency framework, leveraging the strengths and expertise of key implementing partners.',
  operationalUnits = [
    {
      title: 'Strategic Coordination Unit',
      description:
        'Provides overall strategic direction, policy coordination, and stakeholder alignment.',
      icon: <Cog className="w-6 h-6" />,
      responsibilities: [
        'Strategic planning and policy development',
        'Inter-agency coordination and alignment',
        'Stakeholder engagement and partnerships',
        'Performance monitoring and evaluation',
      ],
    },
    {
      title: 'Knowledge Documentation Unit',
      description:
        'Leads indigenous knowledge documentation, preservation, and digitization efforts.',
      icon: <Building className="w-6 h-6" />,
      responsibilities: [
        'IKIA database development and management',
        'Community engagement for knowledge collection',
        'Documentation standards and protocols',
        'Digital preservation and access systems',
      ],
    },
    {
      title: 'Innovation & Development Unit',
      description: 'Facilitates product development, commercialization, and technology transfer.',
      icon: <Network className="w-6 h-6" />,
      responsibilities: [
        'Product development support',
        'Technology transfer facilitation',
        'Innovation ecosystem development',
        'Market linkage and commercialization',
      ],
    },
    {
      title: 'Community Engagement Unit',
      description: 'Ensures community participation, benefit-sharing, and capacity building.',
      icon: <Users className="w-6 h-6" />,
      responsibilities: [
        'Community mobilization and engagement',
        'Capacity building programs',
        'Benefit-sharing mechanisms',
        'Cultural sensitivity and ethics',
      ],
    },
  ],
  implementingPartners = [
    {
      name: 'National Museums of Kenya (NMK)',
      role: 'Lead Implementing Agency',
      description:
        'Provides institutional leadership, cultural heritage expertise, and community networks.',
      logo: '/images/partners/nmk.png',
      url: 'https://museums.or.ke',
      keyContributions: [
        'Institutional leadership and coordination',
        'Cultural heritage and ethnographic expertise',
        'Community networks and relationships',
        'Research and documentation capabilities',
      ],
    },
    {
      name: 'Bioinnovate Africa (BeTA)',
      role: 'Innovation Platform Partner',
      description:
        'Facilitates regional collaboration, innovation support, and technology development.',
      logo: '/images/partners/beta.png',
      url: 'https://bioinnovate-africa.org',
      keyContributions: [
        'Regional bioscience innovation platform',
        'Technology development and transfer',
        'Innovation funding and support',
        'Regional collaboration facilitation',
      ],
    },
    {
      name: 'Kenya Vision 2030 Delivery Secretariat',
      role: 'Policy Alignment Partner',
      description:
        'Ensures alignment with national development priorities and Vision 2030 objectives.',
      logo: '/images/partners/vision-2030.png',
      url: 'https://vision2030.go.ke',
      keyContributions: [
        'Policy alignment and coordination',
        'National development framework integration',
        'Government stakeholder engagement',
        'Strategic planning support',
      ],
    },
    {
      name: 'Ministry of Environment and Forestry',
      role: 'Regulatory and Policy Partner',
      description: 'Provides regulatory oversight, environmental compliance, and policy support.',
      logo: '/images/partners/environment.png',
      keyContributions: [
        'Environmental regulatory oversight',
        'Biodiversity conservation guidance',
        'Policy development and implementation',
        'Sustainable development frameworks',
      ],
    },
  ],
  teamMembers = [
    {
      id: '1',
      name: 'Dr. Sarah Wanjiku',
      title: 'Project Director',
      department: 'Strategic Coordination Unit',
      bio: 'Dr. Sarah Wanjiku brings over 15 years of experience in natural products research and development. She has led numerous initiatives in biodiversity conservation and sustainable development across East Africa.',
      image: '/images/team/team-member-1.jpg',
      email: '<EMAIL>',
      phone: '+254 20 374 2131',
      expertise: [
        'Natural Products Research',
        'Biodiversity Conservation',
        'Strategic Planning',
        'Community Engagement',
      ],
      achievements: [
        'Led 20+ research projects',
        'Published 45 peer-reviewed papers',
        'Recipient of Kenya Science Award 2022',
      ],
      education: [
        'PhD in Natural Products Chemistry - University of Nairobi',
        'MSc in Biochemistry - Kenyatta University',
      ],
    },
    {
      id: '2',
      name: 'Prof. James Mwangi',
      title: 'Research Lead',
      department: 'Knowledge Documentation Unit',
      bio: "Prof. James Mwangi is a renowned expert in traditional medicine and indigenous knowledge systems. His work has been instrumental in documenting and preserving Kenya's rich cultural heritage.",
      image: '/images/team/team-member-2.jpg',
      email: '<EMAIL>',
      phone: '+254 20 374 2132',
      expertise: [
        'Traditional Medicine',
        'Indigenous Knowledge',
        'Ethnobotany',
        'Cultural Heritage',
      ],
      achievements: [
        'Documented 500+ traditional practices',
        'Established 10 community knowledge centers',
        'UNESCO Cultural Heritage Award 2021',
      ],
      education: [
        'PhD in Ethnobotany - University of Cape Town',
        'MSc in Traditional Medicine - University of Nairobi',
      ],
    },
    {
      id: '3',
      name: 'Dr. Grace Kiprotich',
      title: 'Innovation Lead',
      department: 'Technology Transfer Unit',
      bio: 'Dr. Grace Kiprotich specializes in technology transfer and commercialization of natural products. She has successfully facilitated the development of over 30 commercial products from traditional knowledge.',
      image: '/images/team/team-member-3.jpg',
      email: '<EMAIL>',
      phone: '+254 20 374 2133',
      expertise: [
        'Technology Transfer',
        'Product Development',
        'Commercialization',
        'Innovation Management',
      ],
      achievements: [
        '30+ products commercialized',
        'Generated KES 500M in revenue',
        'Innovation Excellence Award 2023',
      ],
      education: [
        'PhD in Innovation Management - Strathmore University',
        'MSc in Chemical Engineering - University of Nairobi',
      ],
    },
    {
      id: '4',
      name: 'Mr. David Ochieng',
      title: 'Community Engagement Lead',
      department: 'Stakeholder Relations Unit',
      bio: 'Mr. David Ochieng has extensive experience in community engagement and stakeholder management. He has worked with over 200 communities across Kenya to document and preserve traditional knowledge.',
      image: '/images/team/team-member-4.jpg',
      email: '<EMAIL>',
      phone: '+254 20 374 2134',
      expertise: [
        'Community Engagement',
        'Stakeholder Management',
        'Cultural Sensitivity',
        'Project Management',
      ],
      achievements: [
        'Engaged 200+ communities',
        'Established 50+ partnerships',
        'Community Leadership Award 2022',
      ],
      education: [
        'MA in Development Studies - University of Nairobi',
        'BA in Social Work - Kenyatta University',
      ],
    },
  ],
}) => {
  const { ref: parallaxRef, y: parallaxY, isMounted } = useParallax({ speed: 0.2 })
  const [selectedMember, setSelectedMember] = useState<TeamMember | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const handleMemberClick = (member: TeamMember) => {
    setSelectedMember(member)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedMember(null)
  }

  return (
    <NPISection
      size="tight"
      className="bg-gradient-to-br from-[#E5E1DC] via-[#CEC9BC] to-[#CABA9C] relative overflow-hidden"
    >
      {/* Parallax Background Elements */}
      {isMounted && (
        <div className="absolute inset-0 pointer-events-none">
          <motion.div
            style={{ transform: `translateY(${parallaxY}px)` }}
            className="absolute top-1/3 left-1/6 w-72 h-72 bg-accent/6 blur-3xl rounded-full"
            animate={{
              x: [0, 40, 0],
              scale: [1, 1.3, 1],
            }}
            transition={{
              duration: 18,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
          <motion.div
            style={{ transform: `translateY(${parallaxY}px)` }}
            className="absolute bottom-1/3 right-1/6 w-96 h-96 bg-primary/4 blur-3xl rounded-full"
            animate={{
              y: [0, -30, 0],
              rotate: [0, 90, 180],
            }}
            transition={{
              duration: 22,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: 1,
            }}
          />
        </div>
      )}

      <div className="relative z-10">
        <NPISectionHeader>
          <NPISectionTitle className="text-[#34170D]">{title}</NPISectionTitle>
          <NPISectionDescription className="text-[#46372A]">{description}</NPISectionDescription>
        </NPISectionHeader>

        {/* Leadership Team */}
        <div className="mb-10">
          <h3 className="text-2xl font-bold text-center mb-6 font-npi text-[#34170D]">
            Leadership Team
          </h3>
          <StaggerContainer>
            <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-6">
              {teamMembers.map((member, index) => (
                <StaggerItem key={member.id} direction="up">
                  <NPICard
                    className="hover:shadow-xl transition-all duration-300 cursor-pointer bg-gradient-to-br from-[#E5E1DC] to-[#CEC9BC] border border-[#8D8F78]/30"
                    onClick={() => handleMemberClick(member)}
                  >
                    <NPICardHeader>
                      <div className="flex flex-col items-center text-center">
                        <div className="w-20 h-20 bg-gradient-to-br from-[#8D8F78] to-[#4C6444] flex items-center justify-center text-white text-xl font-bold mb-4">
                          <User className="w-10 h-10" />
                        </div>
                        <NPICardTitle className="text-lg mb-1 text-[#34170D]">
                          {member.name}
                        </NPICardTitle>
                        <p className="text-sm text-[#6E3C19] font-medium mb-2">{member.title}</p>
                        <p className="text-xs text-[#46372A] font-npi">{member.department}</p>
                      </div>
                    </NPICardHeader>
                    <NPICardContent>
                      <div className="text-center">
                        <div className="flex flex-wrap justify-center gap-1 mb-3">
                          {member.expertise.slice(0, 2).map((skill, skillIndex) => (
                            <span
                              key={skillIndex}
                              className="px-2 py-1 bg-[#CABA9C]/60 text-[#34170D] text-xs font-medium"
                            >
                              {skill}
                            </span>
                          ))}
                        </div>
                        <p className="text-xs text-[#46372A] font-npi">
                          Click to view full profile
                        </p>
                      </div>
                    </NPICardContent>
                  </NPICard>
                </StaggerItem>
              ))}
            </div>
          </StaggerContainer>
        </div>

        {/* Operational Structure */}
        <div className="mb-10">
          <h3 className="text-2xl font-bold text-center mb-6 font-npi text-[#34170D]">
            Operational Structure
          </h3>
          <StaggerContainer>
            <div className="grid md:grid-cols-2 gap-6">
              {operationalUnits.map((unit, index) => (
                <StaggerItem key={index} direction="up">
                  <NPICard className="hover:shadow-lg transition-shadow duration-300 bg-gradient-to-br from-[#E5E1DC] to-[#CEC9BC] border border-[#8D8F78]/30">
                    <NPICardHeader>
                      <div className="flex items-center gap-3 mb-3">
                        <div className="w-12 h-12 bg-[#6E3C19]/20 border border-[#6E3C19]/30 flex items-center justify-center text-[#6E3C19]">
                          {unit.icon}
                        </div>
                        <NPICardTitle className="text-lg text-[#34170D]">{unit.title}</NPICardTitle>
                      </div>
                      <p className="text-[#46372A] font-npi">{unit.description}</p>
                    </NPICardHeader>
                    <NPICardContent>
                      <h5 className="font-semibold mb-3 font-npi">Key Responsibilities:</h5>
                      <ul className="space-y-2">
                        {unit.responsibilities.map((responsibility, respIndex) => (
                          <li key={respIndex} className="flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-sm text-muted-foreground font-npi">
                              {responsibility}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </NPICardContent>
                  </NPICard>
                </StaggerItem>
              ))}
            </div>
          </StaggerContainer>
        </div>

        {/* Implementing Partners */}
        <div>
          <h3 className="text-2xl font-bold text-center mb-6 font-npi text-[#34170D]">
            Implementing Partners
          </h3>
          <StaggerContainer>
            <div className="grid lg:grid-cols-2 gap-8">
              {implementingPartners.map((partner, index) => (
                <StaggerItem key={index} direction="scale">
                  <NPICard className="overflow-hidden hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-[#E5E1DC] to-[#CEC9BC] border border-[#8D8F78]/30">
                    <NPICardHeader>
                      <div className="flex items-start gap-4">
                        <div className="w-16 h-16 bg-[#8A6240]/20 border border-[#8A6240]/30 flex items-center justify-center flex-shrink-0">
                          <div className="text-xs text-center font-npi text-[#4D2D18] font-bold">
                            {partner.name
                              .split(' ')
                              .map((word) => word[0])
                              .join('')}
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <NPICardTitle className="text-lg text-[#34170D]">
                              {partner.name}
                            </NPICardTitle>
                            {partner.url && (
                              <Link
                                href={partner.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-[#6E3C19] hover:text-[#A7795E] transition-colors"
                              >
                                <ExternalLink className="w-4 h-4" />
                              </Link>
                            )}
                          </div>
                          <div className="text-sm font-medium text-[#6E3C19] mb-2 font-npi">
                            {partner.role}
                          </div>
                          <p className="text-sm text-[#46372A] font-npi">{partner.description}</p>
                        </div>
                      </div>
                    </NPICardHeader>
                    <NPICardContent>
                      <h5 className="font-semibold mb-3 font-npi">Key Contributions:</h5>
                      <ul className="space-y-2">
                        {partner.keyContributions.map((contribution, contribIndex) => (
                          <li key={contribIndex} className="flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-secondary rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-sm text-muted-foreground font-npi">
                              {contribution}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </NPICardContent>
                  </NPICard>
                </StaggerItem>
              ))}
            </div>
          </StaggerContainer>
        </div>

        {/* Collaboration Framework */}
        <div className="mt-10">
          <ScrollReveal direction="up" delay={0.3}>
            <NPICard className="bg-gradient-to-r from-primary/5 to-secondary/5 border-2 border-primary/20">
              <NPICardHeader>
                <NPICardTitle className="text-2xl text-center text-primary">
                  Collaborative Framework
                </NPICardTitle>
              </NPICardHeader>
              <NPICardContent>
                <div className="text-center max-w-4xl mx-auto">
                  <p className="text-lg leading-relaxed mb-8 font-npi">
                    NPI&apos;s multi-agency approach ensures comprehensive coverage of all aspects
                    of natural products development, from knowledge documentation to
                    commercialization, while maintaining strong community engagement and cultural
                    sensitivity.
                  </p>

                  <div className="grid md:grid-cols-4 gap-6">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                        <Building className="w-8 h-8 text-primary" />
                      </div>
                      <h5 className="font-semibold mb-2 font-npi">Institutional Strength</h5>
                      <p className="text-sm text-muted-foreground font-npi">
                        Leveraging partner expertise
                      </p>
                    </div>

                    <div className="text-center">
                      <div className="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                        <Network className="w-8 h-8 text-secondary" />
                      </div>
                      <h5 className="font-semibold mb-2 font-npi">Coordinated Action</h5>
                      <p className="text-sm text-muted-foreground font-npi">
                        Unified implementation approach
                      </p>
                    </div>

                    <div className="text-center">
                      <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-3">
                        <Users className="w-8 h-8 text-accent" />
                      </div>
                      <h5 className="font-semibold mb-2 font-npi">Community Focus</h5>
                      <p className="text-sm text-muted-foreground font-npi">
                        Community-centered development
                      </p>
                    </div>

                    <div className="text-center">
                      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                        <Cog className="w-8 h-8 text-primary" />
                      </div>
                      <h5 className="font-semibold mb-2 font-npi">Operational Excellence</h5>
                      <p className="text-sm text-muted-foreground font-npi">
                        Efficient resource utilization
                      </p>
                    </div>
                  </div>
                </div>
              </NPICardContent>
            </NPICard>
          </ScrollReveal>
        </div>
      </div>

      {/* Team Member Modal */}
      <NPITeamModal isOpen={isModalOpen} onClose={handleCloseModal} member={selectedMember} />
    </NPISection>
  )
}
