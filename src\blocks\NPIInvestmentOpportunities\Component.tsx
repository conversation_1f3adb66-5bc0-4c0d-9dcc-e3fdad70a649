'use client'

import React, { useState } from 'react'
import {
  NPISec<PERSON>,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPICard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import Image from 'next/image'
import {
  TrendingUp,
  MapPin,
  Calendar,
  DollarSign,
  Users,
  Target,
  Download,
  Filter,
} from 'lucide-react'

interface InvestmentOpportunity {
  id: string
  title: string
  description: string
  sector: string
  location: string
  investmentRange: string
  expectedROI: string
  timeline: string
  riskLevel: 'Low' | 'Medium' | 'High'
  stage: 'Concept' | 'Development' | 'Pilot' | 'Scale-up'
  image: string
  keyHighlights: string[]
  marketSize: string
  jobsCreated: number
  communityImpact: string
}

interface NPIInvestmentOpportunitiesProps {
  title?: string
  description?: string
  opportunities?: InvestmentOpportunity[]
}

export const NPIInvestmentOpportunitiesBlock: React.FC<NPIInvestmentOpportunitiesProps> = ({
  title = 'Investment Opportunities',
  description = "Discover high-impact investment opportunities in Kenya's natural products sector. From community-based enterprises to innovative technologies, find projects that deliver both financial returns and social impact.",
  opportunities = [
    {
      id: 'aloe-processing-facility',
      title: 'Aloe Vera Processing Facility Expansion',
      description:
        'Scale up successful aloe vera processing operations in Baringo County with modern equipment and expanded production capacity.',
      sector: 'Natural Products Manufacturing',
      location: 'Baringo County',
      investmentRange: 'KES 50-80M',
      expectedROI: '25-35%',
      timeline: '18-24 months',
      riskLevel: 'Medium',
      stage: 'Scale-up',
      image: '/assets/product 1.jpg',
      keyHighlights: [
        'Proven market demand and existing customer base',
        'Sustainable raw material supply from 500+ farmers',
        'Export potential to East African markets',
        'Strong community ownership and support',
      ],
      marketSize: 'KES 2.5B (East Africa)',
      jobsCreated: 200,
      communityImpact: 'Direct benefit to 1,500 households',
    },
    {
      id: 'moringa-value-chain',
      title: 'Moringa Value Chain Development',
      description:
        'Establish integrated moringa production, processing, and marketing system across Northern Kenya counties.',
      sector: 'Nutrition & Health Products',
      location: 'Turkana, Marsabit, Isiolo',
      investmentRange: 'KES 100-150M',
      expectedROI: '30-40%',
      timeline: '24-36 months',
      riskLevel: 'Medium',
      stage: 'Development',
      image: '/assets/product 2.jpg',
      keyHighlights: [
        'High-nutrition product with global demand',
        'Climate-resilient crop suitable for arid areas',
        'Multiple product streams (powder, oil, capsules)',
        'Strong export potential to US and European markets',
      ],
      marketSize: 'USD 8.5B (Global)',
      jobsCreated: 500,
      communityImpact: 'Improved nutrition for 10,000 children',
    },
    {
      id: 'medicinal-plants-research',
      title: 'Medicinal Plants Research & Development Center',
      description:
        'Establish state-of-the-art research facility for traditional medicine validation and product development.',
      sector: 'Pharmaceutical & Research',
      location: 'Nairobi',
      investmentRange: 'KES 200-300M',
      expectedROI: '20-30%',
      timeline: '36-48 months',
      riskLevel: 'High',
      stage: 'Concept',
      image: '/assets/product 3.jpg',
      keyHighlights: [
        'First-of-its-kind facility in East Africa',
        'Partnership with international pharmaceutical companies',
        'IP development and licensing opportunities',
        'Government support and regulatory backing',
      ],
      marketSize: 'USD 150B (Global traditional medicine)',
      jobsCreated: 150,
      communityImpact: 'Validation of 50+ traditional medicines',
    },
    {
      id: 'honey-processing-cooperative',
      title: 'Community Honey Processing Cooperative',
      description:
        'Support Ogiek community in establishing modern honey processing and packaging facility in Mau Forest.',
      sector: 'Food Processing',
      location: 'Nakuru County',
      investmentRange: 'KES 20-35M',
      expectedROI: '20-25%',
      timeline: '12-18 months',
      riskLevel: 'Low',
      stage: 'Pilot',
      image: '/assets/product 4.jpg',
      keyHighlights: [
        'Sustainable forest conservation model',
        'Premium organic honey with certification potential',
        'Strong community governance structure',
        'Growing demand for natural honey products',
      ],
      marketSize: 'KES 15B (Kenya honey market)',
      jobsCreated: 80,
      communityImpact: 'Forest conservation of 1,000 hectares',
    },
    {
      id: 'textile-fashion-brand',
      title: 'Traditional Textile Fashion Brand',
      description:
        'Scale up traditional textile weaving into contemporary fashion brand with international market reach.',
      sector: 'Fashion & Textiles',
      location: 'Machakos County',
      investmentRange: 'KES 40-60M',
      expectedROI: '25-35%',
      timeline: '18-24 months',
      riskLevel: 'Medium',
      stage: 'Scale-up',
      image: '/assets/product 5.jpg',
      keyHighlights: [
        'Unique cultural designs with modern appeal',
        'Skilled artisan network already established',
        'Growing global demand for authentic African fashion',
        'E-commerce and international retail opportunities',
      ],
      marketSize: 'USD 2.5B (African fashion globally)',
      jobsCreated: 300,
      communityImpact: 'Cultural preservation and women empowerment',
    },
    {
      id: 'essential-oils-distillery',
      title: 'Essential Oils Distillery Network',
      description:
        "Establish network of small-scale essential oil distilleries across Kenya's diverse ecological zones.",
      sector: 'Cosmetics & Aromatherapy',
      location: 'Multiple Counties',
      investmentRange: 'KES 80-120M',
      expectedROI: '30-40%',
      timeline: '24-30 months',
      riskLevel: 'Medium',
      stage: 'Development',
      image: '/assets/product 6.jpg',
      keyHighlights: [
        'High-value products with premium pricing',
        'Diverse plant species across different regions',
        'Growing global aromatherapy and wellness market',
        'Sustainable harvesting and community involvement',
      ],
      marketSize: 'USD 18B (Global essential oils)',
      jobsCreated: 400,
      communityImpact: 'Biodiversity conservation and rural income',
    },
  ],
}) => {
  const [selectedSector, setSelectedSector] = useState('All Sectors')
  const [selectedRisk, setSelectedRisk] = useState('All Risk Levels')
  const [selectedStage, setSelectedStage] = useState('All Stages')

  const sectors = ['All Sectors', ...Array.from(new Set(opportunities.map((o) => o.sector)))]
  const riskLevels = ['All Risk Levels', 'Low', 'Medium', 'High']
  const stages = ['All Stages', 'Concept', 'Development', 'Pilot', 'Scale-up']

  const filteredOpportunities = opportunities.filter((opportunity) => {
    return (
      (selectedSector === 'All Sectors' || opportunity.sector === selectedSector) &&
      (selectedRisk === 'All Risk Levels' || opportunity.riskLevel === selectedRisk) &&
      (selectedStage === 'All Stages' || opportunity.stage === selectedStage)
    )
  })

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'Low':
        return 'bg-npi-green-100 text-npi-green-800'
      case 'Medium':
        return 'bg-npi-gold-100 text-npi-gold-800'
      case 'High':
        return 'bg-npi-burgundy-100 text-npi-burgundy-800'
      default:
        return 'bg-npi-grey-100 text-npi-grey-800'
    }
  }

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'Concept':
        return 'bg-npi-brown-100 text-npi-brown-800'
      case 'Development':
        return 'bg-npi-burgundy-100 text-npi-burgundy-800'
      case 'Pilot':
        return 'bg-npi-gold-100 text-npi-gold-800'
      case 'Scale-up':
        return 'bg-npi-green-100 text-npi-green-800'
      default:
        return 'bg-npi-grey-100 text-npi-grey-800'
    }
  }

  return (
    <NPISection>
      <NPISectionHeader>
        <NPISectionTitle>{title}</NPISectionTitle>
        <NPISectionDescription>{description}</NPISectionDescription>
      </NPISectionHeader>

      {/* Filters */}
      <NPICard className="mb-8">
        <NPICardContent className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <Filter className="w-5 h-5 text-muted-foreground" />
            <span className="font-medium font-npi">Filter Opportunities:</span>
          </div>
          <div className="grid md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2 font-npi">Sector</label>
              <select
                value={selectedSector}
                onChange={(e) => setSelectedSector(e.target.value)}
                className="w-full p-2 border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary font-npi"
              >
                {sectors.map((sector) => (
                  <option key={sector} value={sector}>
                    {sector}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 font-npi">Risk Level</label>
              <select
                value={selectedRisk}
                onChange={(e) => setSelectedRisk(e.target.value)}
                className="w-full p-2 border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary font-npi"
              >
                {riskLevels.map((risk) => (
                  <option key={risk} value={risk}>
                    {risk}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 font-npi">Stage</label>
              <select
                value={selectedStage}
                onChange={(e) => setSelectedStage(e.target.value)}
                className="w-full p-2 border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary font-npi"
              >
                {stages.map((stage) => (
                  <option key={stage} value={stage}>
                    {stage}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </NPICardContent>
      </NPICard>

      {/* Opportunities Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {filteredOpportunities.map((opportunity) => (
          <NPICard
            key={opportunity.id}
            className="overflow-hidden hover:shadow-xl transition-all duration-300 aspect-square flex flex-col"
          >
            <div className="relative h-32 w-full flex-shrink-0">
              <Image
                src={opportunity.image}
                alt={opportunity.title}
                fill
                className="object-cover"
              />
              <div className="absolute top-2 left-2 flex gap-1">
                <span
                  className={`px-1.5 py-0.5 rounded-full text-xs font-medium ${getRiskColor(opportunity.riskLevel)}`}
                >
                  {opportunity.riskLevel}
                </span>
                <span
                  className={`px-1.5 py-0.5 rounded-full text-xs font-medium ${getStageColor(opportunity.stage)}`}
                >
                  {opportunity.stage}
                </span>
              </div>
            </div>

            <NPICardHeader className="p-3 flex-shrink-0">
              <NPICardTitle className="text-sm leading-tight mb-1">
                {opportunity.title}
              </NPICardTitle>
              <div className="text-xs text-primary font-medium mb-1 font-npi">
                {opportunity.sector}
              </div>
              <p className="text-muted-foreground text-xs leading-tight line-clamp-2 font-npi">
                {opportunity.description}
              </p>
            </NPICardHeader>

            <NPICardContent className="p-3 flex-1 flex flex-col">
              {/* Key Metrics - Compact Grid */}
              <div className="grid grid-cols-2 gap-2 mb-3 text-xs">
                <div className="flex items-center gap-1">
                  <DollarSign className="w-3 h-3 text-muted-foreground" />
                  <div>
                    <div className="font-medium font-npi text-xs">
                      {opportunity.investmentRange}
                    </div>
                    <div className="text-muted-foreground text-xs">Investment</div>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <TrendingUp className="w-3 h-3 text-muted-foreground" />
                  <div>
                    <div className="font-medium font-npi text-xs">{opportunity.expectedROI}</div>
                    <div className="text-muted-foreground text-xs">ROI</div>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <MapPin className="w-3 h-3 text-muted-foreground" />
                  <div>
                    <div className="font-medium font-npi text-xs">{opportunity.location}</div>
                    <div className="text-muted-foreground text-xs">Location</div>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="w-3 h-3 text-muted-foreground" />
                  <div>
                    <div className="font-medium font-npi text-xs">{opportunity.timeline}</div>
                    <div className="text-muted-foreground text-xs">Timeline</div>
                  </div>
                </div>
              </div>

              {/* Impact Metrics - Compact */}
              <div className="bg-primary/5 p-2 mb-2 flex-1">
                <h5 className="font-semibold mb-1 text-xs font-npi">Impact:</h5>
                <div className="grid grid-cols-2 gap-1 text-xs">
                  <div>
                    <div className="font-medium text-primary font-npi text-xs">
                      {opportunity.jobsCreated}
                    </div>
                    <div className="text-muted-foreground text-xs">Jobs</div>
                  </div>
                  <div>
                    <div className="font-medium text-primary font-npi text-xs">
                      {opportunity.marketSize}
                    </div>
                    <div className="text-muted-foreground text-xs">Market</div>
                  </div>
                </div>
              </div>

              {/* Key Highlights - Compact */}
              <div className="mb-2 flex-1">
                <h5 className="font-semibold mb-1 text-xs font-npi">Highlights:</h5>
                <ul className="space-y-0.5">
                  {opportunity.keyHighlights.slice(0, 2).map((highlight, index) => (
                    <li key={index} className="flex items-start gap-1 text-xs">
                      <div className="w-1 h-1 bg-primary rounded-full mt-1.5 flex-shrink-0"></div>
                      <span className="text-muted-foreground font-npi line-clamp-1">
                        {highlight}
                      </span>
                    </li>
                  ))}
                  {opportunity.keyHighlights.length > 2 && (
                    <li className="text-xs text-primary font-npi">
                      +{opportunity.keyHighlights.length - 2} more
                    </li>
                  )}
                </ul>
              </div>
            </NPICardContent>

            <NPICardFooter className="flex gap-1 p-3 flex-shrink-0">
              <NPIButton asChild variant="primary" className="flex-1 text-xs py-1">
                <Link href={`/partnerships/opportunities/${opportunity.id}`}>View Details</Link>
              </NPIButton>
              <NPIButton asChild variant="outline" size="sm" className="px-2">
                <Link href={`/partnerships/opportunities/${opportunity.id}/prospectus.pdf`}>
                  <Download className="w-3 h-3" />
                </Link>
              </NPIButton>
            </NPICardFooter>
          </NPICard>
        ))}
      </div>

      {/* Results Summary */}
      <div className="text-center mt-8">
        <p className="text-muted-foreground font-npi">
          Showing {filteredOpportunities.length} of {opportunities.length} investment opportunities
        </p>
      </div>
    </NPISection>
  )
}
