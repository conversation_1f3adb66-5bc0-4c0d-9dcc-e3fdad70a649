import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Media Gallery - Resources - Natural Products Industry Initiative',
  description:
    'Explore our comprehensive media gallery featuring videos, photos, and multimedia content showcasing NPI initiatives, community impact, and natural products development across Kenya.',
}

const mediaGalleryPageLayout = [
  {
    blockType: 'npiMediaGalleryHero' as const,
    title: 'Media Gallery',
    backgroundImage: '/assets/product 3.jpg',
  },
  {
    blockType: 'npiMediaGalleryContent' as const,
    id: 'media-content',
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Media Impact & Reach',
    variant: 'secondary',
  },
]

export default function MediaGalleryPage() {
  return (
    <article className="pb-24">
      <RenderBlocks blocks={mediaGalleryPageLayout} />
    </article>
  )
}
