import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import { NPIScrollToTop } from '@/components/ui/npi-scroll-to-top'
import PageClient from './page.client'

export const metadata: Metadata = {
  title: 'Operations & Structure - About NPI',
  description:
    "Learn about NPI's operational structure, multi-agency framework, and implementing partners working together to drive sustainable development.",
}

const operationsStructurePageLayout = [
  {
    blockType: 'npiOperationsHero' as const,
  },
  {
    blockType: 'npiOperationsStructure' as const,
  },
]

export default function OperationsStructurePage() {
  return (
    <>
      <PageClient />
      <article className="pb-12" style={{ scrollBehavior: 'smooth' }}>
        <RenderBlocks blocks={operationsStructurePageLayout} />
      </article>
      <NPIScrollToTop showAfter={400} />
    </>
  )
}
