import type { Block } from 'payload'

export const NPIIntroduction: Block = {
  slug: 'npiIntroduction',
  interfaceName: 'NPIIntroductionBlock',
  fields: [
    {
      name: 'title',
      type: 'text',
      defaultValue: 'About the Natural Products Industry Initiative',
      label: 'Title',
    },
    {
      name: 'description',
      type: 'text',
      defaultValue: "Transforming Kenya's Vision 2030 into reality through sustainable natural products development.",
      label: 'Description',
    },
    {
      name: 'content',
      type: 'textarea',
      defaultValue: "The Natural Products Industry (NPI) Initiative is a groundbreaking multi-agency program designed to harness Kenya's rich indigenous knowledge and natural resources for sustainable economic development. Aligned with Kenya Vision 2030 and the Medium Term Plan IV (MTP IV), NPI serves as a catalyst for transforming traditional knowledge into market-ready products while preserving cultural heritage and empowering local communities.",
      label: 'Content',
    },
  ],
  labels: {
    plural: 'NPI Introduction Blocks',
    singular: 'NPI Introduction Block',
  },
}
