'use client'

import React, { useState } from 'react'
import {
  NPISec<PERSON>,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPICard,
  NPICardHeader,
  NPICardTitle,
  NPICardDescription,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import Image from 'next/image'
import { Filter, MapPin, Calendar, Users, Target, ArrowRight } from 'lucide-react'

interface Program {
  id: string
  title: string
  description: string
  category: string
  pillar: string
  location: string
  duration: string
  status: 'active' | 'completed' | 'upcoming' | 'planning'
  participants: number
  budget: string
  image: string
  objectives: string[]
  partners: string[]
  link: string
}

interface NPIProgramsListingProps {
  title?: string
  description?: string
  programs?: Program[]
}

export const NPIProgramsListingBlock: React.FC<NPIProgramsListingProps> = ({
  title = 'Our Programs & Projects',
  description = "Comprehensive initiatives transforming Kenya's natural products landscape through community-driven innovation, capacity building, and sustainable development.",
  programs = [
    {
      id: 'knowledge-documentation',
      title: 'Indigenous Knowledge Documentation Project',
      description:
        "Systematic documentation of traditional knowledge systems across Kenya's 47 counties, creating a comprehensive digital repository for preservation and access.",
      category: 'Knowledge Preservation',
      pillar: 'Indigenous Knowledge Documentation',
      location: 'All 47 Counties',
      duration: '2022-2025',
      status: 'active',
      participants: 2500,
      budget: 'KES 150M',
      image: '/assets/product 1.jpg',
      objectives: [
        'Document traditional knowledge from all counties',
        'Create digital preservation systems',
        'Establish community protocols',
        'Build local documentation capacity',
      ],
      partners: ['National Museums of Kenya', 'County Governments', 'Community Organizations'],
      link: '/programs/ikia-documentation',
    },
    {
      id: 'community-enterprises',
      title: 'Community-Based Natural Products Enterprises',
      description:
        'Supporting local communities in developing sustainable enterprises based on traditional knowledge and natural resources.',
      category: 'Enterprise Development',
      pillar: 'Product Development & Commercialization',
      location: '15 Counties',
      duration: '2023-2026',
      status: 'active',
      participants: 800,
      budget: 'KES 200M',
      image: '/assets/product 2.jpg',
      objectives: [
        'Establish 50 community enterprises',
        'Create sustainable value chains',
        'Build market linkages',
        'Generate employment opportunities',
      ],
      partners: [
        'Kenya Association of Manufacturers',
        'Export Promotion Council',
        'Microfinance Institutions',
      ],
      link: '/programs/community-enterprises',
    },
    {
      id: 'youth-innovation',
      title: 'Youth Innovation & Entrepreneurship Program',
      description:
        'Empowering young entrepreneurs to develop innovative natural products and technologies while preserving traditional knowledge.',
      category: 'Youth Empowerment',
      pillar: 'Capacity Building & Empowerment',
      location: '10 Counties',
      duration: '2024-2027',
      status: 'upcoming',
      participants: 500,
      budget: 'KES 80M',
      image: '/assets/product 3.jpg',
      objectives: [
        'Train 500 young entrepreneurs',
        'Support 100 startup ventures',
        'Create innovation hubs',
        'Foster technology adoption',
      ],
      partners: ['Universities', 'Innovation Hubs', 'Private Sector Partners'],
      link: '/programs/youth-innovation',
    },
    {
      id: 'ip-protection',
      title: 'Traditional Knowledge IP Protection Initiative',
      description:
        'Safeguarding traditional knowledge through intellectual property registration and legal protection mechanisms.',
      category: 'Legal Protection',
      pillar: 'Intellectual Property Protection',
      location: 'Nationwide',
      duration: '2023-2025',
      status: 'active',
      participants: 300,
      budget: 'KES 60M',
      image: '/assets/product 4.jpg',
      objectives: [
        'Register 50 traditional knowledge patents',
        'Train communities on IP rights',
        'Establish legal frameworks',
        'Create benefit-sharing mechanisms',
      ],
      partners: [
        'Kenya Industrial Property Institute',
        'Legal Aid Organizations',
        'International IP Bodies',
      ],
      link: '/programs/ip-protection',
    },
    {
      id: 'women-empowerment',
      title: 'Women in Natural Products Leadership',
      description:
        'Empowering women to lead natural products development initiatives and benefit from traditional knowledge commercialization.',
      category: 'Women Empowerment',
      pillar: 'Capacity Building & Empowerment',
      location: '20 Counties',
      duration: '2023-2026',
      status: 'active',
      participants: 1200,
      budget: 'KES 120M',
      image: '/assets/product 5.jpg',
      objectives: [
        'Train 1200 women entrepreneurs',
        'Establish women-led cooperatives',
        'Create market access opportunities',
        'Build leadership capacity',
      ],
      partners: ["Women's Organizations", 'Cooperative Societies', 'Financial Institutions'],
      link: '/programs/women-empowerment',
    },
    {
      id: 'research-innovation',
      title: 'Natural Products Research & Innovation Hub',
      description:
        'Establishing state-of-the-art research facilities for natural products development, testing, and commercialization.',
      category: 'Research & Development',
      pillar: 'Product Development & Commercialization',
      location: 'Nairobi, Mombasa, Kisumu',
      duration: '2024-2028',
      status: 'planning',
      participants: 200,
      budget: 'KES 300M',
      image: '/assets/product 6.jpg',
      objectives: [
        'Establish 3 research centers',
        'Develop 100 new products',
        'Support 50 research projects',
        'Create technology transfer mechanisms',
      ],
      partners: [
        'Universities',
        'Research Institutions',
        'Private Sector',
        'International Partners',
      ],
      link: '/programs/research-innovation',
    },
  ],
}) => {
  const [selectedCategory, setSelectedCategory] = useState('All Categories')
  const [selectedPillar, setSelectedPillar] = useState('All Pillars')
  const [selectedStatus, setSelectedStatus] = useState('All Status')

  const categories = ['All Categories', ...Array.from(new Set(programs.map((p) => p.category)))]
  const pillars = ['All Pillars', ...Array.from(new Set(programs.map((p) => p.pillar)))]
  const statuses = ['All Status', 'active', 'completed', 'upcoming', 'planning']

  const filteredPrograms = programs.filter((program) => {
    return (
      (selectedCategory === 'All Categories' || program.category === selectedCategory) &&
      (selectedPillar === 'All Pillars' || program.pillar === selectedPillar) &&
      (selectedStatus === 'All Status' || program.status === selectedStatus)
    )
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      case 'upcoming':
        return 'bg-orange-100 text-orange-800'
      case 'planning':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <NPISection size="lg">
      <NPISectionHeader className="mb-8">
        <NPISectionTitle>{title}</NPISectionTitle>
        <NPISectionDescription>{description}</NPISectionDescription>
      </NPISectionHeader>

      {/* Filters */}
      <NPICard className="mb-8">
        <NPICardContent className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <Filter className="w-5 h-5 text-muted-foreground" />
            <span className="font-medium font-npi">Filter Programs:</span>
          </div>
          <div className="grid md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2 font-npi">Category</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full p-2 border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary font-npi"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 font-npi">Strategic Pillar</label>
              <select
                value={selectedPillar}
                onChange={(e) => setSelectedPillar(e.target.value)}
                className="w-full p-2 border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary font-npi"
              >
                {pillars.map((pillar) => (
                  <option key={pillar} value={pillar}>
                    {pillar}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 font-npi">Status</label>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-full p-2 border border-border rounded focus:outline-none focus:ring-2 focus:ring-primary font-npi"
              >
                {statuses.map((status) => (
                  <option key={status} value={status}>
                    {status === 'All Status'
                      ? status
                      : status.charAt(0).toUpperCase() + status.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </NPICardContent>
      </NPICard>

      {/* Programs Grid */}
      <div className="grid lg:grid-cols-2 gap-8">
        {filteredPrograms.map((program) => (
          <NPICard
            key={program.id}
            className="overflow-hidden hover:shadow-xl transition-all duration-300"
          >
            <div className="relative h-48 w-full">
              <Image src={program.image} alt={program.title} fill className="object-cover" />
              <div className="absolute top-4 left-4">
                <span
                  className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(program.status)}`}
                >
                  {program.status.charAt(0).toUpperCase() + program.status.slice(1)}
                </span>
              </div>
              <div className="absolute top-4 right-4">
                <span className="bg-black/70 text-white px-2 py-1 rounded text-xs font-npi">
                  {program.category}
                </span>
              </div>
            </div>

            <NPICardHeader>
              <NPICardTitle className="text-xl">{program.title}</NPICardTitle>
              <NPICardDescription className="text-base leading-relaxed">
                {program.description}
              </NPICardDescription>
            </NPICardHeader>

            <NPICardContent>
              {/* Program Details */}
              <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-muted-foreground" />
                  <span className="font-npi">{program.location}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  <span className="font-npi">{program.duration}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-muted-foreground" />
                  <span className="font-npi">
                    {program.participants.toLocaleString()} participants
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Target className="w-4 h-4 text-muted-foreground" />
                  <span className="font-npi">{program.budget}</span>
                </div>
              </div>

              {/* Strategic Pillar */}
              <div className="mb-4">
                <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded font-npi">
                  {program.pillar}
                </span>
              </div>

              {/* Key Objectives */}
              <div className="mb-4">
                <h5 className="font-semibold mb-2 text-sm font-npi">Key Objectives:</h5>
                <ul className="space-y-1">
                  {program.objectives.slice(0, 2).map((objective, index) => (
                    <li key={index} className="flex items-start gap-2 text-sm">
                      <div className="w-1 h-1 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-muted-foreground font-npi">{objective}</span>
                    </li>
                  ))}
                  {program.objectives.length > 2 && (
                    <li className="text-sm text-primary font-npi">
                      +{program.objectives.length - 2} more objectives
                    </li>
                  )}
                </ul>
              </div>
            </NPICardContent>

            <NPICardFooter>
              <NPIButton asChild variant="outline" className="w-full">
                <Link href={program.link}>
                  Learn More <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </NPIButton>
            </NPICardFooter>
          </NPICard>
        ))}
      </div>

      {/* Results Summary */}
      <div className="text-center mt-8">
        <p className="text-muted-foreground font-npi">
          Showing {filteredPrograms.length} of {programs.length} programs
        </p>
      </div>
    </NPISection>
  )
}
