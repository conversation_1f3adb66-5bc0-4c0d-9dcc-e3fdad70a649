import React from 'react'
import Image from 'next/image'

interface NPIStrategicAlignmentHeroProps {
  title?: string
  subtitle?: string
  backgroundImage?: string
}

export const NPIStrategicAlignmentHeroBlock: React.FC<NPIStrategicAlignmentHeroProps> = ({
  title = 'Strategic Alignment',
  subtitle = "Aligned with Kenya's key development frameworks",
  backgroundImage = '/assets/hero image.jpg',
}) => {
  return (
    <section className="min-h-screen relative overflow-hidden -mt-24 pt-24">
      {/* Background Image */}
      <div className="absolute inset-0 w-full h-full">
        <Image
          src={backgroundImage}
          alt="Strategic Alignment background"
          fill
          priority
          className="w-full h-full object-cover"
          sizes="100vw"
        />
      </div>

      {/* Overlay with bright color variants */}
      <div className="absolute inset-0 bg-gradient-to-b from-[#4D2D18]/30 via-[#8A6240]/50 to-[#CABA9C]/70" />

      {/* Top Center Title */}
      <div className="absolute top-32 left-1/2 transform -translate-x-1/2 z-30 text-center max-w-5xl px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight text-white mb-4">
          {title}
        </h1>
        {subtitle && (
          <p className="text-lg sm:text-xl md:text-2xl text-white/90 font-medium">{subtitle}</p>
        )}
      </div>

      {/* Bottom Left Text with bright colors */}
      <div className="absolute bottom-8 left-8 z-30 max-w-sm">
        <div className="bg-[#E5E1DC]/90 backdrop-blur-md p-6 border-l-4 border-[#4D2D18]">
          <p className="text-sm sm:text-base md:text-lg leading-relaxed text-[#2F2C29]">
            Strategically aligned with Vision 2030, MTP IV, BeTA, and National Museums of Kenya
            frameworks.
          </p>
        </div>
      </div>

      {/* Bottom Right Feature Card with bright colors */}
      <div className="absolute bottom-8 right-8 z-30">
        <div className="bg-[#CEC9BC]/95 backdrop-blur-md p-6 shadow-lg max-w-xs border border-[#4D2D18]/30">
          <h3 className="text-lg sm:text-xl font-bold mb-2 text-[#34170D]">National Alignment</h3>
          <p className="text-sm sm:text-base mb-4 text-[#46372A]">
            Coordinated efforts toward national transformation goals and sustainable development.
          </p>
          <a
            href="#"
            className="text-[#6E3C19] hover:text-[#A7795E] text-sm font-medium transition-colors"
          >
            Learn More &rarr;
          </a>
        </div>
      </div>
    </section>
  )
}
