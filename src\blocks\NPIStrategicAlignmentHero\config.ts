import type { Block } from 'payload'

export const NPIStrategicAlignmentHero: Block = {
  slug: 'npiStrategicAlignmentHero',
  interfaceName: 'NPIStrategicAlignmentHeroBlock',
  fields: [
    {
      name: 'title',
      type: 'text',
      defaultValue: 'Strategic Alignment',
    },
    {
      name: 'subtitle',
      type: 'text',
      defaultValue: 'Aligned with Kenya\'s key development frameworks',
    },
    {
      name: 'backgroundImage',
      type: 'text',
      defaultValue: '/assets/Vision 2030.png',
    },
  ],
  labels: {
    plural: 'NPI Strategic Alignment Heroes',
    singular: 'NPI Strategic Alignment Hero',
  },
}
