import React from 'react'
import { NPIHero, NPIHeroTitle, NPIHeroSubtitle, NPIHeroActions } from '@/components/ui/npi-hero'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import { Users, Heart, Lightbulb, FileText, ArrowDown, MessageSquare } from 'lucide-react'

interface NPIGetInvolvedHeroProps {
  title?: string
  subtitle?: string
  totalOpportunities?: number
  activeVolunteers?: number
  ongoingProjects?: number
}

export const NPIGetInvolvedHeroBlock: React.FC<NPIGetInvolvedHeroProps> = ({
  title = "Get Involved",
  subtitle = "Join the movement to transform Kenya's natural products sector. Whether you're a researcher, entrepreneur, community leader, or passionate individual, there's a place for you in our mission.",
  totalOpportunities = 15,
  activeVolunteers = 200,
  ongoingProjects = 25
}) => {
  const engagementTypes = [
    {
      icon: <Users className="w-6 h-6" />,
      title: "Community Engagement",
      count: "8 Opportunities",
      description: "Work directly with communities"
    },
    {
      icon: <Lightbulb className="w-6 h-6" />,
      title: "Research & Innovation",
      count: "5 Opportunities",
      description: "Contribute to knowledge development"
    },
    {
      icon: <Heart className="w-6 h-6" />,
      title: "Volunteer Programs",
      count: "12 Programs",
      description: "Support various initiatives"
    },
    {
      icon: <FileText className="w-6 h-6" />,
      title: "Professional Services",
      count: "6 Areas",
      description: "Share your expertise"
    }
  ]

  return (
    <NPIHero variant="gradient" className="min-h-[85vh]">
      <div className="max-w-6xl mx-auto text-center">
        <NPIHeroTitle>{title}</NPIHeroTitle>
        
        <NPIHeroSubtitle>{subtitle}</NPIHeroSubtitle>
        
        {/* Key Statistics */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="text-3xl font-bold text-white mb-2 font-npi">{totalOpportunities}</div>
            <div className="text-white/80 font-npi">Ways to Get Involved</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="text-3xl font-bold text-white mb-2 font-npi">{activeVolunteers}+</div>
            <div className="text-white/80 font-npi">Active Volunteers</div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <div className="text-3xl font-bold text-white mb-2 font-npi">{ongoingProjects}</div>
            <div className="text-white/80 font-npi">Ongoing Projects</div>
          </div>
        </div>

        {/* Engagement Types */}
        <div className="grid md:grid-cols-4 gap-4 mb-8">
          {engagementTypes.map((type, index) => (
            <div key={index} className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 hover:bg-white/20 transition-all duration-300">
              <div className="bg-white/20 w-10 h-10 rounded-lg flex items-center justify-center mx-auto mb-3 text-white">
                {type.icon}
              </div>
              <h3 className="font-semibold text-white mb-1 text-sm font-npi">{type.title}</h3>
              <div className="text-white/90 text-xs mb-1 font-npi">{type.count}</div>
              <p className="text-white/70 text-xs font-npi">{type.description}</p>
            </div>
          ))}
        </div>

        <NPIHeroActions>
          <NPIButton asChild size="lg" variant="cream">
            <Link href="#opportunities">
              <ArrowDown className="w-5 h-5 mr-2" />
              Explore Opportunities
            </Link>
          </NPIButton>
          
          <NPIButton asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary">
            <Link href="/get-involved/volunteer">
              <Heart className="w-5 h-5 mr-2" />
              Volunteer Now
            </Link>
          </NPIButton>
          
          <NPIButton asChild size="lg" variant="accent">
            <Link href="/contact">
              <MessageSquare className="w-5 h-5 mr-2" />
              Share Your Ideas
            </Link>
          </NPIButton>
        </NPIHeroActions>
      </div>
    </NPIHero>
  )
}
