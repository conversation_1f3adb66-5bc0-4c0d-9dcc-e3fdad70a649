import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import PageClient from './page.client'

export const metadata: Metadata = {
  title: 'Our Partners - Natural Products Industry Initiative',
  description:
    "Meet our partners who are driving sustainable development in Kenya's natural products sector. Discover the organizations and individuals committed to community empowerment and traditional knowledge preservation.",
}

const partnersPageLayout = [
  {
    blockType: 'npiPartnersHero' as const,
  },
  {
    blockType: 'npiPartnersShowcase' as const,
    id: 'partners-showcase',
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Partnership Network',
    variant: 'secondary',
  },
]

export default function PartnersPage() {
  return (
    <>
      <PageClient />
      <article className="pb-24">
        <RenderBlocks blocks={partnersPageLayout} />
      </article>
    </>
  )
}
