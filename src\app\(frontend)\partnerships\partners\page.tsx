import React, { Fragment } from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import PageClient from './page.client'
import { blockComponents } from '@/blocks'

export const metadata: Metadata = {
  title: 'Our Partners - Natural Products Industry Initiative',
  description:
    "Meet our partners who are driving sustainable development in Kenya's natural products sector. Discover the organizations and individuals committed to community empowerment and traditional knowledge preservation.",
}

const partnersPageLayout = [
  {
    blockType: 'npiPartnersHero' as const,
  },
  {
    blockType: 'npiPartnersShowcase' as const,
    id: 'partners-showcase',
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Partnership Network',
    variant: 'secondary',
  },
]

export default function PartnersPage() {
  return (
    <>
      <PageClient />
      <article>
        {partnersPageLayout.map((block, index) => {
          const { blockType } = block

          if (blockType && blockType in blockComponents) {
            const Block = blockComponents[blockType]

            if (Block) {
              const isFirstBlock = index === 0

              return (
                <section
                  className={`
                    ${isFirstBlock ? '' : '-mt-1'}
                    relative
                    ${
                      index % 6 === 0
                        ? 'bg-[#E5E1DC]'
                        : index % 6 === 1
                          ? 'bg-[#8D8F78]/15'
                          : index % 6 === 2
                            ? 'bg-[#CABA9C]/20'
                            : index % 6 === 3
                              ? 'bg-[#4C6444]/12'
                              : index % 6 === 4
                                ? 'bg-[#2F2C29]/8'
                                : 'bg-[#CEC9BC]/25'
                    }
                  `}
                  key={index}
                >
                  <div className="w-full">
                    {/* @ts-expect-error there may be some mismatch between the expected types here */}
                    <Block {...block} disableInnerContainer />
                  </div>
                </section>
              )
            }
          }
          return null
        })}
      </article>
    </>
  )
}
