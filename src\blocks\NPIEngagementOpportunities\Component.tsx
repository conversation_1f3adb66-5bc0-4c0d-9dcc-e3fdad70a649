import React from 'react'
import {
  NPISec<PERSON>,
  NPISectionHeader,
  NPISectionTitle,
  NPISectionDescription,
} from '@/components/ui/npi-section'
import {
  NPICard,
  NPICardHeader,
  NPICardTitle,
  NPICardContent,
  NPICardFooter,
} from '@/components/ui/npi-card'
import { NPIButton } from '@/components/ui/npi-button'
import Link from 'next/link'
import {
  Users,
  Lightbulb,
  Heart,
  FileText,
  Briefcase,
  GraduationCap,
  MapPin,
  Clock,
  ArrowRight,
} from 'lucide-react'

interface EngagementOpportunity {
  id: string
  title: string
  description: string
  category: string
  type: 'volunteer' | 'professional' | 'research' | 'internship' | 'community'
  location: string
  timeCommitment: string
  requirements: string[]
  benefits: string[]
  icon: React.ReactNode
  color: string
  urgent?: boolean
}

interface NPIEngagementOpportunitiesProps {
  title?: string
  description?: string
  opportunities?: EngagementOpportunity[]
}

export const NPIEngagementOpportunitiesBlock: React.FC<NPIEngagementOpportunitiesProps> = ({
  title = 'Ways to Get Involved',
  description = "Discover meaningful opportunities to contribute to Kenya's natural products development while gaining valuable experience and making lasting impact.",
  opportunities = [
    {
      id: 'community-documentation-volunteer',
      title: 'Community Knowledge Documentation Volunteer',
      description:
        'Work with local communities to document traditional knowledge and practices, helping preserve cultural heritage for future generations.',
      category: 'Knowledge Preservation',
      type: 'volunteer',
      location: 'Various Counties',
      timeCommitment: '2-3 days per month',
      requirements: [
        'Fluency in local languages preferred',
        'Cultural sensitivity and respect',
        'Basic documentation skills',
        'Commitment to community protocols',
      ],
      benefits: [
        'Cultural immersion experience',
        'Documentation skills training',
        'Certificate of participation',
        'Networking with communities',
      ],
      icon: <FileText className="w-6 h-6" />,
      color: 'from-blue-500 to-blue-600',
      urgent: false,
    },
    {
      id: 'research-assistant-medicinal-plants',
      title: 'Research Assistant - Medicinal Plants Study',
      description:
        'Support ongoing research into traditional medicinal plants, including field collection, laboratory analysis, and data management.',
      category: 'Research & Development',
      type: 'research',
      location: 'Nairobi & Field Sites',
      timeCommitment: '20-30 hours per week',
      requirements: [
        "Bachelor's degree in relevant field",
        'Research experience preferred',
        'Attention to detail',
        'Willingness to travel',
      ],
      benefits: [
        'Research experience and training',
        'Publication opportunities',
        'Professional networking',
        'Competitive stipend',
      ],
      icon: <Lightbulb className="w-6 h-6" />,
      color: 'from-green-500 to-green-600',
      urgent: true,
    },
    {
      id: 'youth-entrepreneurship-mentor',
      title: 'Youth Entrepreneurship Mentor',
      description:
        'Guide young entrepreneurs in developing natural products businesses, providing mentorship and business development support.',
      category: 'Capacity Building',
      type: 'professional',
      location: 'Multiple Counties',
      timeCommitment: '4-6 hours per week',
      requirements: [
        'Business or entrepreneurship experience',
        'Mentoring or coaching skills',
        'Passion for youth development',
        'Flexible schedule',
      ],
      benefits: [
        'Leadership development',
        'Impact on youth lives',
        'Professional recognition',
        'Mentoring skills certification',
      ],
      icon: <Users className="w-6 h-6" />,
      color: 'from-purple-500 to-purple-600',
      urgent: false,
    },
    {
      id: 'digital-marketing-specialist',
      title: 'Digital Marketing Specialist',
      description:
        'Help community enterprises develop online presence and marketing strategies for their natural products.',
      category: 'Marketing & Communications',
      type: 'professional',
      location: 'Remote/Field visits',
      timeCommitment: '10-15 hours per week',
      requirements: [
        'Digital marketing experience',
        'Social media expertise',
        'Content creation skills',
        'Understanding of rural markets',
      ],
      benefits: [
        'Portfolio development',
        'Rural market insights',
        'Professional references',
        'Flexible working arrangement',
      ],
      icon: <Briefcase className="w-6 h-6" />,
      color: 'from-orange-500 to-orange-600',
      urgent: false,
    },
    {
      id: 'university-internship-program',
      title: 'University Internship Program',
      description:
        'Semester-long internship program for university students to gain hands-on experience in natural products development.',
      category: 'Education & Training',
      type: 'internship',
      location: 'Nairobi & Field Sites',
      timeCommitment: '3-6 months',
      requirements: [
        'Currently enrolled university student',
        'Relevant field of study',
        'Academic supervisor approval',
        'Minimum 3.0 GPA',
      ],
      benefits: [
        'Academic credit available',
        'Professional experience',
        'Mentorship and guidance',
        'Potential job opportunities',
      ],
      icon: <GraduationCap className="w-6 h-6" />,
      color: 'from-teal-500 to-teal-600',
      urgent: false,
    },
    {
      id: 'community-liaison-coordinator',
      title: 'Community Liaison Coordinator',
      description:
        'Facilitate communication between NPI and local communities, ensuring effective collaboration and mutual understanding.',
      category: 'Community Relations',
      type: 'community',
      location: 'Specific Counties',
      timeCommitment: 'Part-time, flexible',
      requirements: [
        'Strong community connections',
        'Excellent communication skills',
        'Cultural knowledge and sensitivity',
        'Conflict resolution abilities',
      ],
      benefits: [
        'Community leadership role',
        'Communication skills development',
        'Professional networking',
        'Community impact',
      ],
      icon: <Heart className="w-6 h-6" />,
      color: 'from-red-500 to-red-600',
      urgent: true,
    },
  ],
}) => {
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'volunteer':
        return 'bg-blue-100 text-blue-800'
      case 'professional':
        return 'bg-green-100 text-green-800'
      case 'research':
        return 'bg-purple-100 text-purple-800'
      case 'internship':
        return 'bg-orange-100 text-orange-800'
      case 'community':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <NPISection>
      <NPISectionHeader>
        <NPISectionTitle>{title}</NPISectionTitle>
        <NPISectionDescription>{description}</NPISectionDescription>
      </NPISectionHeader>

      <div className="grid lg:grid-cols-2 gap-8">
        {opportunities.map((opportunity) => (
          <NPICard
            key={opportunity.id}
            className="overflow-hidden hover:shadow-xl transition-all duration-300"
          >
            {/* Header with gradient */}
            <div className={`bg-gradient-to-r ${opportunity.color} p-6 text-white relative`}>
              {opportunity.urgent && (
                <div className="absolute top-4 right-4">
                  <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                    Urgent
                  </span>
                </div>
              )}
              <div className="flex items-center gap-4 mb-4">
                <div className="bg-white/20 p-3 rounded-lg">{opportunity.icon}</div>
                <div className="flex-1">
                  <NPICardTitle className="text-white text-xl mb-2">
                    {opportunity.title}
                  </NPICardTitle>
                  <div className="flex items-center gap-2">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(opportunity.type)} bg-white/20 text-white`}
                    >
                      {opportunity.type.charAt(0).toUpperCase() + opportunity.type.slice(1)}
                    </span>
                    <span className="text-white/80 text-sm font-npi">{opportunity.category}</span>
                  </div>
                </div>
              </div>
              <p className="text-white/90 leading-relaxed font-npi">{opportunity.description}</p>
            </div>

            {/* Content */}
            <NPICardContent className="p-6">
              {/* Basic Info */}
              <div className="grid grid-cols-2 gap-4 mb-6 text-sm">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-muted-foreground" />
                  <span className="font-npi">{opportunity.location}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-muted-foreground" />
                  <span className="font-npi">{opportunity.timeCommitment}</span>
                </div>
              </div>

              <div className="space-y-4">
                {/* Requirements */}
                <div>
                  <h4 className="font-semibold mb-2 text-foreground font-npi">Requirements:</h4>
                  <ul className="space-y-1">
                    {opportunity.requirements.slice(0, 3).map((requirement, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-muted-foreground font-npi">{requirement}</span>
                      </li>
                    ))}
                    {opportunity.requirements.length > 3 && (
                      <li className="text-sm text-primary font-npi">
                        +{opportunity.requirements.length - 3} more requirements
                      </li>
                    )}
                  </ul>
                </div>

                {/* Benefits */}
                <div>
                  <h4 className="font-semibold mb-2 text-foreground font-npi">
                    What You&apos;ll Gain:
                  </h4>
                  <ul className="space-y-1">
                    {opportunity.benefits.slice(0, 3).map((benefit, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-muted-foreground font-npi">{benefit}</span>
                      </li>
                    ))}
                    {opportunity.benefits.length > 3 && (
                      <li className="text-sm text-green-600 font-npi">
                        +{opportunity.benefits.length - 3} more benefits
                      </li>
                    )}
                  </ul>
                </div>
              </div>
            </NPICardContent>

            <NPICardFooter className="flex gap-2">
              <NPIButton asChild variant="primary" className="flex-1">
                <Link href={`/get-involved/apply/${opportunity.id}`}>
                  Apply Now <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </NPIButton>
              <NPIButton asChild variant="outline" size="sm">
                <Link href={`/get-involved/${opportunity.id}`}>Learn More</Link>
              </NPIButton>
            </NPICardFooter>
          </NPICard>
        ))}
      </div>

      {/* Call to Action */}
      <div className="mt-12 text-center">
        <NPICard className="bg-gradient-to-r from-primary/5 to-secondary/5 border-2 border-primary/20">
          <NPICardContent className="p-8">
            <h3 className="text-2xl font-bold mb-4 font-npi">
              Don&apos;t See What You&apos;re Looking For?
            </h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto font-npi">
              We&apos;re always looking for passionate individuals with unique skills and
              perspectives. Reach out to us with your ideas and let&apos;s explore how you can
              contribute to our mission.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <NPIButton asChild size="lg" variant="primary">
                <Link href="/contact">Share Your Ideas</Link>
              </NPIButton>
              <NPIButton asChild size="lg" variant="outline">
                <Link href="/get-involved/newsletter">Join Our Newsletter</Link>
              </NPIButton>
            </div>
          </NPICardContent>
        </NPICard>
      </div>
    </NPISection>
  )
}
