import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'
import PageClient from './page.client'

export const metadata: Metadata = {
  title: 'Investment Opportunities - Natural Products Industry Initiative',
  description:
    "Explore investment opportunities in Kenya's natural products sector. Discover ready-to-invest projects that drive sustainable development and community empowerment.",
}

const investmentOpportunitiesPageLayout = [
  {
    blockType: 'npiInvestmentOpportunitiesHero' as const,
  },
  {
    blockType: 'npiInvestmentOpportunities' as const,
    id: 'opportunities',
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Investment Impact',
    variant: 'primary',
  },
]

export default function InvestmentOpportunitiesPage() {
  return (
    <>
      <PageClient />
      <article className="pb-24">
        <RenderBlocks blocks={investmentOpportunitiesPageLayout} />
      </article>
    </>
  )
}
