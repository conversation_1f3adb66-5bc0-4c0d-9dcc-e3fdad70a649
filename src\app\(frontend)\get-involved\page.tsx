import React from 'react'
import { RenderBlocks } from '@/blocks/RenderBlocks'
import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Get Involved - Natural Products Industry Initiative',
  description:
    "Join the movement to transform Kenya's natural products sector. Discover volunteer opportunities, professional roles, research positions, and ways to contribute to our mission.",
}

const getInvolvedPageLayout = [
  {
    blockType: 'npiGetInvolvedHero' as const,
  },
  {
    blockType: 'npiEngagementOpportunities' as const,
    id: 'opportunities',
  },
  {
    blockType: 'npiSuccessStories' as const,
    title: 'Volunteer Impact Stories',
    description:
      'See how our volunteers and partners are making a difference in communities across Kenya.',
  },
  {
    blockType: 'npiStatistics' as const,
    title: 'Community Impact',
    variant: 'pattern',
  },
  {
    blockType: 'npiPartners' as const,
    title: 'Join Our Network',
    description:
      'Connect with organizations and individuals committed to sustainable development and community empowerment.',
  },
]

export default function GetInvolvedPage() {
  return (
    <article className="pt-16 pb-24">
      <RenderBlocks blocks={getInvolvedPageLayout} />
    </article>
  )
}
